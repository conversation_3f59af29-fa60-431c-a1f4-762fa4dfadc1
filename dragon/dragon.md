# ZclMltSrvHbtMngr
CHeartbeatHandler 作为主控制器，管理多个广播器和监听器
CHeartbeat_Broadcaster 负责向指定地址发送心跳包
CHeartbeat_Listerner 负责监听心跳包，内部使用 CHeartbeat_Receiver 接收数据
CHeartbeat_Receiver 负责底层UDP数据接收

# ZclMltSrvOnlineMngr
CSrvOnlineManager 作为总控制器，协调各个子组件
CSrvSelfStatusMonitor 监控本地服务器状态并发送心跳
CSrvHeartbeatMonitor 监听其他服务器心跳并更新状态
CServerSwitchHandler 根据服务器状态执行切换逻辑

# ZcsServer

### common
通用定义和配置文件：CommuDef.h
线程同步工具：CsLocker.h
消息处理组件：SttpMsgProductor.h/cpp, ZxMsgCaster.h/cpp
观察者模式组件：ZxObserver.h/cpp, ZxPublisher.h/cpp
API接口定义：多个*API.h文件
服务管理组件：ZxSrvOnlineManagerWrapper.h/cpp
版本历史记录：ZxCommuServer_common_update_history.cpp

### server

##### ZcsCliMngr

会话池管理：维护所有客户端会话的生命周期
协议库加载：动态加载不同的通信协议库
数据库交互：从数据库读取站点配置信息
命令处理：处理来自上层的控制命令
在线管理：配合服务器在线管理器实现主备切换

##### ZcsDataHdl

ZcsDataHdl（数据处理中心） 是 ZcsServer 通信服务器的核心数据处理组件，负责接收、分类、处理和存储来自各个子站的 STTP 协议消息。它是整个通信系统的数据中枢，承担着数据流转、消息分发和持久化存储的关键职责。

##### ZcsFrontMgr

ZcsFrontMgr（前置管理器） 是 ZcsServer 通信服务器的进程管理中心，负责管理和监控各个子站的前置通信进程。它是一个独立的可执行程序，可以作为 Windows 服务或 Linux 守护进程运行，确保各个子站的通信进程始终保持运行状态。

##### ZcsFrontSrv

ZcsFrontSrv（前置服务器） 是 ZcsServer 通信服务器的核心前置通信进程，负责与具体子站设备进行直接通信。它是一个可执行程序，由 ZcsFrontMgr 管理器启动和监控，每个子站通常对应一个独立的 ZcsFrontSrv 进程实例。

##### ZcsMain

ZcsMain（主服务器程序） 是 ZcsServer 通信服务器的核心主程序，作为整个通信系统的统一入口和控制中心。它负责加载和启动所有必要的动态库组件，协调各个子系统的运行，是整个 ZcsServer 架构的总控制器。

##### ZcsSrvMgrMain

ZcsSrvMgrMain（服务端管理器主程序） 是 ZcsServer 通信服务器的核心服务端管理组件，负责管理与上级调度中心或其他系统的通信连接。它是一个独立的可执行程序，承担着服务端监听、客户端连接管理、协议处理和消息转发的关键职责。


##### ZcsSrvMngr

ZcsSrvMngr（服务器管理器动态库） 是 ZcsServer 通信服务器的核心服务器管理动态库，与 ZcsSrvMgrMain 不同，它是一个动态链接库（DLL），被其他组件（如 ZcsMain）加载和调用。它提供了服务器连接管理、协议处理和客户端会话管理的核心功能。

##### ZcsSsbMngr

ZcsSsbMngr（订阅中心管理器） 是 ZcsServer 通信服务器的核心消息分发中心，实现了经典的发布者-观察者模式。它作为动态链接库提供服务，负责管理系统中所有的消息发布者和观察者，实现消息的订阅、分发和路由功能。


#### 组件关系详细说明
1. 主控制层
ZcsMain（主服务器程序）

角色：系统总控制器和统一入口
关系：加载和启动所有动态库组件
职责：协调各个子系统运行，管理组件生命周期
2. 独立进程层
ZcsFrontMgr（前置管理器）

角色：进程管理中心
关系：管理和监控所有 ZcsFrontSrv 进程
职责：确保前置通信进程始终运行
ZcsFrontSrv（前置服务器）

角色：前置通信进程
关系：被 ZcsFrontMgr 管理，每个子站对应一个实例
职责：与具体子站设备直接通信
ZcsSrvMgrMain（服务端管理器主程序）

角色：独立的服务端管理程序
关系：与上级调度中心直接通信
职责：处理上级系统的连接和协议
3. 动态库服务层
ZcsSsbMngr（订阅中心管理器）

角色：消息分发中心
关系：被其他组件加载，实现发布-订阅模式
职责：管理消息发布者和观察者，实现消息路由
ZcsDataHdl（数据处理中心）

角色：数据处理核心
关系：通过订阅中心接收消息，与数据库交互
职责：数据存储、处理和持久化
ZcsSrvMngr（服务器管理器动态库）

角色：服务器管理功能库
关系：被 ZcsMain 等程序加载使用
职责：提供服务器连接管理和协议处理
ZcsCliMngr（客户端管理器）

角色：客户端会话管理器
关系：管理下级设备连接
职责：会话池管理、协议库加载

### pro

##### common

###### 基础设施组件
ThreadManager 线程管理器
ZxCommonDBFunction 数据库功能
SftpHdl SFTP处理器

###### 协议特化实现
各种MsgAttach和FuncHandle

###### 协议处理框架
ASDUHandler ASDU处理器
ASDUFactory 工厂
MainFlowWrapper 主流程包装器-> SttpServerHandler


# do
bool CServerEngine::Start()
{
    if (!Init())  // 1. 初始化配置和日志
    {
        return false;
    }
    
    //start SubscribeCenter  // 2. 启动订阅中心（消息总线）
    if (StartSubscribeCenter() == 0){
        m_LogFile.Add("StartSubscribeCenter successfully");		
    }
    
    //start DataHandler      // 3. 启动数据处理中心
    if (StartDataHandler() == 0){
        m_LogFile.Add("StartDataHandler successfully");		
    }
    
    //start server          // 4. 启动服务器管理器
    if (StartServerMgr() == 0){
        m_LogFile.Add("startServerMgr successfully");		
    }
    
    //start client          // 5. 启动客户端管理器（可选）
    if (m_bStartClientManager && (m_nServerRole != 2 ))
    {
        if (StartClientMgr() == 0){
            m_LogFile.Add("StartClientMgr successfully");		
        }
        
        //start SrvOnlineManager  // 6. 启动在线管理器
        if (StartSrvOnlineManager() == 0){
            m_LogFile.Add("StartSrvOnlineManager successfully");		
        }
    }	
    return true;	
}




子站设备 (IEC 104)
    ↓ TCP Socket (端口102)
push::create_listensock() 
    ↓ 监听连接
push::select_loop()
    ↓ 接收数据
push::rsock() 
    ↓ RFC1006解析
push::filterTpkt()
    ↓ TPDU处理
vMsg队列
    ↓ 消息转换
CXJ104APCIHandler::OnReceiveData()
    ↓ APCI层处理
CHuNTc103ASDUHandler
    ↓ ASDU层处理
STTP消息生成
    ↓ 消息总线
_sendSttp2Msgbus()
    ↓ 动态库接口
ZxBusSwapLib
    ↓ 跨进程通信
ZcsMain (消息总线中心)