3.模块设计
3.1ZclMltSrvOnlineMngr通讯服务器服务器在线切换动态库
3.1.1自动识别各前置管辖的厂站列表
在结构体stXJSrvOnlineManager的成员iReserved中扩充通信前置类型枚举：1-61850前置；2-104Vlan；3-私有规约透传前置。
当iReserved=1时，获取规约库名称中包含“61850”的规约ID，在通道表中查询所有使用61850规约通信的厂站ID，再根据服务器ID识别是否为本服务器管辖，从而获取到本服务器管辖的所有使用61850规约通信的厂站列表。
当iReserved=2时，获取厂站表中子站功能类型（stn_fun_type）为7-湖南透传子站且为本服务器管辖的厂站列表。
当iReserved=3时，获取厂站表中通道模式（reserve4）为3-私有规约透传或5-混合透传，且为本服务器管辖的厂站列表。
3.1.2厂站断开后自动切换服务器
在多机热切服务器配置表（tb_commu_server_config）中新增一个字段，用于存放厂站断开多久后释放管辖权，如厂站断开5分钟后放弃管辖权。
服务器A和服务器B之间通过发送UDP心跳，通知对方本机状态以及管辖厂站的通信状态。对于服务器A来说，当发现前置A设置的厂站通信状态为断开时，记录断开时间，若设置的时间范围（如5分钟）内厂站通信状态仍未恢复，则通知前置A放弃管辖权。当服务器B收到服务器A发送的心跳时，判断其管辖厂站的通信状态，记录厂站断开时间并判断是否到达接管时间，到达接管时间则通知前置B接管该厂站。
服务器两两一组互相切换。
3.2ZclMltSrvHbtMngr通讯服务器服务器心跳模块动态库
3.2.1心跳维护机制
为避免出现多类型前置运行在同一台服务器上，使用同一个心跳端口，重复监听出现异常，现规定各类型前置使用的心跳端口在配置的广播端口（tb_commu_server_config. heartbeatport）基础上加2乘前置类型。
如tb_commu_server_config. Heartbeatport=17255，配置为常规保信前置（iReserved=0），则其使用的接收端口为17255，发送端口为17256；配置为61850前置（iReserved=1），其使用的接收端口为17255+2*1=17257，发送端口为17258；配置为104Vlan（iReserved=2），其使用的接收端口为17255+2*2=17259，发送端口为17260；配置为私有规约透传前置（iReserved=3），其使用的接收端口为17255+2*3=17261，发送端口为17262。
3.3ZcsFrontMgr采集前置管理主程序
3.3.161850进程管理
根据ZclMltSrvOnlineMngr回调出的管辖厂站列表，监视61850厂站进程，当厂站释放管辖权后，杀掉对应的厂站进程不再拉起，反之，拉起厂站进程。
3.3.261850厂站通信状态管理
方案一：周期查询厂站表，获取管辖厂站的通信状态，并通过SetStationLoadStatus方法通知给ZclMltSrvOnlineMngr。
不适用于透传环境，因为透传环境下厂站通信状态由104Vlan模块判断并发出，通过查询厂站通信状态判断是否切换并不合理。
方案二：通过判断厂站是否有sreadd进程判断通信情况。
存在误判的情况，如查询周期刚好遇到61850的重连周期，此时探测到sreadd进程存在，通信情况就会被误判为通。
3.4ZcsFrontSrv采集前置主程序
3.4.1根据采集前置类型加载ZclMltSrvOnlineMngr
当采集前置类型为常规103前置（即传入的参数为空或传入的参数为103）时，加载ZclMltSrvOnlineMngr动态库，否则不加载。
3.5ZcsCliMngr客户端管理动态库
3.5.1根据采集前置类型加载ZclMltSrvOnlineMngr
当采集前置类型为常规103前置（即m_strProcessType=103）时，加载ZclMltSrvOnlineMngr动态库，否则不加载。
对于61850前置，还需将厂站放入队列m_PermitCtrlStationSet，并启动厂站规约，避免厂站无法通信的情况发生。
3.6Zcs104VlanSrv
3.6.1根据采集前置类型加载ZclMltSrvOnlineMngr
1、加载ZclMltSrvOnlineMngr动态库，获取方法地址。
2、注册服务器切换回调函数，即调用RegisterSrvSwitchCallback方法。通过回调获取需要接管的厂站ID，并拉起对应厂站进程。
3、设置参数stXJSrvOnlineManager，其中，服务器ID（chServerID）来自于ZcsServer.ini配置文件中serverid，日志路径（szLog_path）、日志等级（iLog_level）、日志保存天数（iLogDay）与104Vlan配置保持一致即可，是否记录报文日志（bRecordMsg）来源于tb_commu_local_config表中msg_log字段，该字段值为1则配置为true，否则为false，通信前置类型（iReserved）配置为2，启动ZclMltSrvOnlineMngr，即调用StartSrvOnLineManager方法。
4、在厂站通信状态发生变更时，调用设置子站加载状态（SetStationLoadStatus）方法更新厂站状态，便于在线切换动态库判断是否需要接管或不接管厂站。
3.7nx_ga_manager采集工厂管理主程序
与104Vlan基本一致。