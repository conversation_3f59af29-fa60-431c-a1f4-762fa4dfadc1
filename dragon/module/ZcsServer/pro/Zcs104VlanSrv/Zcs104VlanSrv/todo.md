# 动态库加载和初始化 
xj_load_library(const char* libName) - 加载动态库
xj_get_addr(XJHANDLE handle, const char* funcName) - 获取函数地址
xj_free_library(XJHANDLE handle) - 释放动态库
# 配置参数读取
GetIniKey(const char* iniFile, const char* section, const char* key, char* value) - 读取配置值
GetIniKey(const char* iniFile, const char* section, const char* key, int& value) - 读取整数配置
GetIniKey(const char* iniFile, const char* section, const char* key, bool& value) - 读取布尔配置
# 数据库读取配置
AddField(SQL_DATA& sqlData, const char* fieldName, int dataType) - 添加查询字段
AddCondition(SQL_DATA& sqlData, const char* fieldName, int dataType, const char* value, int condition) - 添加查询条件
RDSelect(int tableType, SQL_DATA& sqlData, REALDATA_CONDITION& condition, char* error, CMemSet* result) - 执行查询
GetRecordSet(const char* sql, CMemSet& result, char* error) - 直接SQL查询
# 回调函数注册 
    104Vlan厂站状态变化 → HandleFrontLinkChange() → UpdateStationCommStatus() → SetStationLoadStatus() → ZclMltSrvOnlineMngr
# 厂站状态同步 
    104Vlan厂站状态变化 → HandleFrontLinkChange() → UpdateStationCommStatus() → SetStationLoadStatus() → ZclMltSrvOnlineMngr
# 厂站进程管理 
push(CMessageLog* log, bool* exitFlag) - 构造函数
start(const std::string& ip, int port, const std::string& stationId, std::map<std::string, pthread_t>& threadIds) - 启动厂站进程
析构函数会自动清理资源
# 日志
Add(const char* msg, int level) - 添加日志
FormatAdd(int level, const char* format, ...) - 格式化日志
SetLogLevel(int level) - 设置日志级别
SetLogPath(const char* path) - 设置日志路径
SetLogSaveDays(int days) - 设置日志保存天数