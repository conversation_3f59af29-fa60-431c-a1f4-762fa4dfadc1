# 基于EventLoop的IPC消息总线开发计划书

## 项目概述

**项目目标**：基于现有的EventLoop机制，开发一个用于进程间通信（IPC）的发布-订阅模式消息总线，支持传递 `CommonMessage` 和 `EventMessage` 结构体，作为core动态库的一部分提供服务。

**核心需求**：
- 单机多进程间的IPC通信
- 发布-订阅（pub/sub）模式
- 基于Unix Domain Socket实现
- 完全集成现有EventLoop架构
- 支持 `CommonMessage` 和 `EventMessage` 结构体传输
- 作为core动态库的组成部分

**消息类型**：
```cpp
// 通用消息结构
struct CommonMessage {
    MessageType type{MessageType::COMMAND};
    ObjectId source_id{INVALID_ID};
    ObjectId target_id{INVALID_ID};
    std::string invoke_id;
    std::vector<uint8_t> data;
    bool b_lastmsg{true};  // 是否为最后一帧消息
};

// 事件消息结构
struct EventMessage {
    EventType event_type{0};
    DeviceUUID device_uuid;
    ObjectId source_id{INVALID_ID};
    std::string description;
    std::vector<uint8_t> data;
};
```

## 技术架构设计

**选择Unix Domain Socket的理由**：
1. 性能优于FIFO，支持双向通信
2. 完美融入现有Channel/EventLoop架构
3. 便于后续扩展（可升级为TCP支持跨机器通信）
4. 与现有TcpServer使用模式一致

**核心组件**：
- `MessageBusServer`：消息总线服务端
- `MessageBusClient`：消息总线客户端
- `MessageBusConnection`：连接管理
- `MessageSerializer`：消息序列化/反序列化器

**消息序列化方案**：
- 使用二进制序列化，确保高性能
- 消息头包含：消息类型标识、消息长度
- 支持 `CommonMessage` 和 `EventMessage` 的序列化/反序列化

## 开发任务分解

### 阶段一：架构设计与序列化机制

**[ ] 任务1：设计消息序列化协议**
- 定义消息帧格式（消息头 + 消息体）
- 实现 `CommonMessage` 序列化/反序列化
- 实现 `EventMessage` 序列化/反序列化
- 处理字节序和对齐问题
- 预期成果：完整的消息序列化器类
- 验证方法：单元测试验证序列化往返一致性

**[ ] 任务2：设计消息总线核心接口**
- 定义MessageBusServer类接口
- 定义MessageBusClient类接口
- 设计回调函数类型（CommonMessageHandler、EventMessageHandler）
- 定义订阅管理接口（按消息类型订阅）
- 预期成果：完整的头文件接口定义
- 验证方法：接口设计评审，确保与现有EventLoop架构兼容

**[ ] 任务3：设计Unix Domain Socket地址管理**
- 定义socket文件路径规范（如/tmp/messagebus.sock）
- 设计地址冲突处理机制
- 考虑权限和安全性设置
- 预期成果：地址管理策略文档和相关工具函数
- 验证方法：多进程启动测试，确保地址管理正确

### 阶段二：服务端实现

**[ ] 任务4：实现MessageBusServer基础框架**
- 创建基于EventLoop的服务端类
- 集成现有Acceptor组件处理连接
- 实现连接管理（连接建立、断开、清理）
- 预期成果：可启动并接受连接的服务端框架
- 验证方法：使用简单客户端测试连接建立

**[ ] 任务5：实现订阅管理机制**
- 设计按消息类型的订阅表数据结构
- 实现CommonMessage和EventMessage的分别订阅
- 支持客户端断线时自动清理订阅
- 预期成果：完整的订阅管理模块
- 验证方法：单元测试验证订阅表操作正确性

**[ ] 任务6：实现消息转发机制**
- 实现基于消息类型的消息路由
- 支持消息广播到对应类型的订阅者
- 处理发送失败和客户端断线情况
- 集成消息序列化器
- 预期成果：可靠的消息转发功能
- 验证方法：多客户端消息收发测试

### 阶段三：客户端实现

**[ ] 任务7：实现MessageBusClient基础框架**
- 创建基于EventLoop的客户端类
- 集成现有Connector组件处理连接
- 实现自动重连机制
- 预期成果：可连接服务端的客户端框架
- 验证方法：连接建立和重连功能测试

**[ ] 任务8：实现发布功能**
- 实现publishCommonMessage接口
- 实现publishEventMessage接口
- 集成消息序列化器进行消息编码
- 处理发送缓冲和流控
- 预期成果：完整的消息发布功能
- 验证方法：发布消息并在服务端验证接收

**[ ] 任务9：实现订阅功能**
- 实现subscribeCommonMessage/unsubscribeCommonMessage接口
- 实现subscribeEventMessage/unsubscribeEventMessage接口
- 实现消息接收和反序列化
- 设计消息回调机制
- 预期成果：完整的消息订阅功能
- 验证方法：订阅消息并验证回调触发

### 阶段四：协议实现与优化

**[ ] 任务10：实现控制协议**
- 定义订阅/取消订阅控制消息格式
- 实现客户端-服务端握手协议
- 处理协议版本兼容性
- 预期成果：完整的控制协议实现
- 验证方法：协议兼容性测试

**[ ] 任务11：优化消息序列化性能**
- 实现零拷贝优化（尽可能减少内存拷贝）
- 优化std::vector<uint8_t>和std::string的序列化
- 添加消息压缩支持（可选）
- 预期成果：高性能的序列化实现
- 验证方法：性能基准测试

### 阶段五：集成与构建

**[ ] 任务12：集成到CMake构建系统**
- 将MessageBus相关源文件添加到core库
- 更新CMakeLists.txt包含新的源文件
- 确保依赖关系正确（依赖现有的base/net模块）
- 预期成果：MessageBus作为core库的一部分成功编译
- 验证方法：完整构建测试，确保库文件正确生成

**[ ] 任务13：添加头文件到公共接口**
- 将MessageBus头文件添加到include目录
- 更新命名空间结构（zexuan::net::messagebus）
- 确保与现有代码风格一致
- 预期成果：用户可通过include使用MessageBus功能
- 验证方法：外部程序链接core库并使用MessageBus接口

### 阶段六：测试与验证

**[ ] 👤 任务14：编写单元测试**
- 为MessageSerializer编写单元测试
- 为MessageBusServer编写单元测试
- 为MessageBusClient编写单元测试
- 测试异常情况处理（连接断开、格式错误等）
- 预期成果：完整的单元测试套件
- 验证方法：所有测试用例通过，代码覆盖率达标

**[ ] 任务15：创建示例程序**
- 编写CommonMessage发布者示例程序
- 编写EventMessage发布者示例程序
- 编写订阅者示例程序（同时订阅两种消息）
- 创建多进程通信演示
- 预期成果：可运行的示例程序和使用文档
- 验证方法：示例程序正确运行，消息收发正常

**[ ] 👤 任务16：集成测试与性能验证**
- 多进程并发测试
- 消息吞吐量和延迟测试
- 长时间运行稳定性测试
- 内存泄漏检测
- 预期成果：性能基准数据和稳定性报告
- 验证方法：达到预期性能指标，无内存泄漏

### 阶段七：文档与优化

**[ ] 👤 任务17：编写API文档**
- 编写MessageBus使用指南
- 创建API参考文档
- 提供最佳实践建议
- 说明与现有base类型的集成方式
- 预期成果：完整的用户文档
- 验证方法：文档评审，确保清晰易懂

**[ ] 任务18：代码优化与重构**
- 性能优化（减少内存拷贝、优化数据结构）
- 代码重构（提高可读性和可维护性）
- 错误处理完善
- 预期成果：高质量的生产就绪代码
- 验证方法：代码评审，性能对比测试

## 依赖关系与开发顺序

**关键路径**：
1. 任务1（序列化协议）→ 任务2（接口设计）→ 任务4（服务端框架）→ 任务7（客户端框架）
2. 任务3（地址管理）必须在任务4之前完成
3. 任务5-6（服务端功能）与任务8-9（客户端功能）可并行开发
4. 任务10（控制协议）在基础功能完成后进行
5. 任务12-13（构建集成）在核心功能完成后进行
6. 任务14-18（测试与优化）在功能实现后进行

**并行开发机会**：
- 任务5-6（服务端功能）与任务8-9（客户端功能）可并行
- 任务11（性能优化）可与其他开发任务并行
- 任务15（示例程序）可在基础功能完成后提前开始

## 风险识别与应对

**技术风险**：
1. 结构体序列化的跨平台兼容性问题
   - 应对：使用标准的字节序转换，避免直接内存拷贝
2. std::vector<uint8_t>序列化性能问题
   - 应对：实现高效的变长数据序列化方案
3. Unix Domain Socket与现有EventLoop集成复杂度
   - 应对：先实现简单原型验证可行性

**项目风险**：
1. 消息结构体定义变更
   - 应对：设计版本兼容的序列化格式
2. 与现有代码兼容性问题
   - 应对：严格遵循现有代码规范和架构模式

## 预期交付物

1. **核心代码**：MessageBusServer、MessageBusClient、MessageSerializer类及相关组件
2. **头文件**：完整的公共API接口
3. **示例程序**：CommonMessage和EventMessage的发布者和订阅者示例
4. **测试代码**：单元测试和集成测试
5. **文档**：API文档和使用指南
6. **构建配置**：更新的CMakeLists.txt

这份开发计划书为您的IPC消息总线项目提供了清晰的实施路径，特别针对 `CommonMessage` 和 `EventMessage` 结构体的传输需求进行了优化设计。建议按照任务顺序逐步实施，每完成一个阶段后进行评审和测试，确保项目质量。