基于EventLoop的IPC消息总线开发计划书
项目概述
项目目标：基于现有的EventLoop机制，开发一个用于进程间通信（IPC）的发布-订阅模式消息总线，支持JSON消息格式，作为core动态库的一部分提供服务。

核心需求：

单机多进程间的IPC通信
发布-订阅（pub/sub）模式
基于Unix Domain Socket实现
完全集成现有EventLoop架构
支持JSON消息格式（使用nlohmann/json）
作为core动态库的组成部分
技术架构设计
选择Unix Domain Socket的理由：

性能优于FIFO，支持双向通信
完美融入现有Channel/EventLoop架构
便于后续扩展（可升级为TCP支持跨机器通信）
与现有TcpServer使用模式一致
核心组件：

MessageBusServer：消息总线服务端
MessageBusClient：消息总线客户端
MessageBusConnection：连接管理
Message：消息封装类
开发任务分解
阶段一：架构设计与接口定义
[ ] 任务1：设计消息总线核心接口

定义MessageBusServer类接口
定义MessageBusClient类接口
设计Message消息格式（JSON schema）
定义回调函数类型（发布、订阅、连接状态等）
预期成果：完整的头文件接口定义
验证方法：接口设计评审，确保与现有EventLoop架构兼容
[ ] 任务2：设计Unix Domain Socket地址管理

定义socket文件路径规范（如/tmp/messagebus.sock）
设计地址冲突处理机制
考虑权限和安全性设置
预期成果：地址管理策略文档和相关工具函数
验证方法：多进程启动测试，确保地址管理正确
阶段二：服务端实现
[ ] 任务3：实现MessageBusServer基础框架

创建基于EventLoop的服务端类
集成现有Acceptor组件处理连接
实现连接管理（连接建立、断开、清理）
预期成果：可启动并接受连接的服务端框架
验证方法：使用telnet或nc工具测试连接建立
[ ] 任务4：实现订阅管理机制

设计topic订阅表数据结构
实现订阅注册、取消订阅功能
支持客户端断线时自动清理订阅
预期成果：完整的订阅管理模块
验证方法：单元测试验证订阅表操作正确性
[ ] 任务5：实现消息转发机制

实现基于topic的消息路由
支持消息广播到所有订阅者
处理发送失败和客户端断线情况
预期成果：可靠的消息转发功能
验证方法：多客户端消息收发测试
阶段三：客户端实现
[ ] 任务6：实现MessageBusClient基础框架

创建基于EventLoop的客户端类
集成现有Connector组件处理连接
实现自动重连机制
预期成果：可连接服务端的客户端框架
验证方法：连接建立和重连功能测试
[ ] 任务7：实现发布功能

实现publish接口，支持JSON消息发送
集成nlohmann/json进行消息序列化
处理发送缓冲和流控
预期成果：完整的消息发布功能
验证方法：发布消息并在服务端验证接收
[ ] 任务8：实现订阅功能

实现subscribe/unsubscribe接口
实现消息接收和JSON反序列化
设计消息回调机制
预期成果：完整的消息订阅功能
验证方法：订阅消息并验证回调触发
阶段四：协议设计与消息格式
[ ] 任务9：设计消息协议格式

定义控制消息格式（订阅、取消订阅等）
定义数据消息格式（topic、payload等）
实现消息编解码器
预期成果：完整的协议规范和编解码实现
验证方法：协议兼容性测试
[ ] 任务10：集成JSON消息支持

封装nlohmann/json的使用
提供便捷的JSON消息构造和解析接口
处理JSON格式错误和异常
预期成果：用户友好的JSON消息接口
验证方法：各种JSON格式的消息收发测试
阶段五：集成与构建
[ ] 任务11：集成到CMake构建系统

将MessageBus相关源文件添加到core库
更新CMakeLists.txt包含新的源文件
确保nlohmann/json依赖正确链接
预期成果：MessageBus作为core库的一部分成功编译
验证方法：完整构建测试，确保库文件正确生成
[ ] 任务12：添加头文件到公共接口

将MessageBus头文件添加到include目录
更新命名空间结构（zexuan::net::messagebus）
确保与现有代码风格一致
预期成果：用户可通过include使用MessageBus功能
验证方法：外部程序链接core库并使用MessageBus接口
阶段六：测试与验证
[ ] 👤 任务13：编写单元测试

为MessageBusServer编写单元测试
为MessageBusClient编写单元测试
测试异常情况处理（连接断开、格式错误等）
预期成果：完整的单元测试套件
验证方法：所有测试用例通过，代码覆盖率达标
[ ] 任务14：创建示例程序

编写简单的发布者示例程序
编写简单的订阅者示例程序
创建多进程通信演示
预期成果：可运行的示例程序和使用文档
验证方法：示例程序正确运行，消息收发正常
[ ] 👤 任务15：集成测试与性能验证

多进程并发测试
消息吞吐量和延迟测试
长时间运行稳定性测试
预期成果：性能基准数据和稳定性报告
验证方法：达到预期性能指标，无内存泄漏
阶段七：文档与优化
[ ] 👤 任务16：编写API文档

编写MessageBus使用指南
创建API参考文档
提供最佳实践建议
预期成果：完整的用户文档
验证方法：文档评审，确保清晰易懂
[ ] 任务17：代码优化与重构

性能优化（减少内存拷贝、优化数据结构）
代码重构（提高可读性和可维护性）
错误处理完善
预期成果：高质量的生产就绪代码
验证方法：代码评审，性能对比测试
依赖关系与开发顺序
关键路径：

任务1-2（架构设计）→ 任务3（服务端框架）→ 任务6（客户端框架）
任务9（协议设计）必须在任务4-5、7-8之前完成
任务10（JSON集成）与任务7-8并行开发
任务11-12（构建集成）在核心功能完成后进行
任务13-17（测试与优化）在功能实现后进行
并行开发机会：

任务4-5（服务端功能）与任务7-8（客户端功能）可并行
任务10（JSON集成）可与其他开发任务并行
任务14（示例程序）可在基础功能完成后提前开始
风险识别与应对
技术风险：

Unix Domain Socket与现有EventLoop集成复杂度
应对：先实现简单原型验证可行性
消息序列化性能问题
应对：设计时考虑零拷贝优化方案
项目风险：

需求变更导致架构调整
应对：采用模块化设计，便于扩展
与现有代码兼容性问题
应对：严格遵循现有代码规范和架构模式
预期交付物
核心代码：MessageBusServer、MessageBusClient类及相关组件
头文件：完整的公共API接口
示例程序：发布者和订阅者示例
测试代码：单元测试和集成测试
文档：API文档和使用指南
构建配置：更新的CMakeLists.txt