#include <doctest/doctest.h>
#include <zexuan/net/message_codec.hpp>
#include <zexuan/net/buffer.hpp>
#include <zexuan/base/types/structs.hpp>

#include <memory>
#include <chrono>

using namespace zexuan::net;
using namespace zexuan::base;

// Mock TcpConnection for testing
class MockTcpConnection {
public:
    MockTcpConnection() = default;
    void send(Buffer* buffer) {
        sentData_.append(buffer->peek(), buffer->readableBytes());
    }
    
    const std::string& getSentData() const { return sentData_; }
    void clearSentData() { sentData_.clear(); }
    
private:
    std::string sentData_;
};

using MockTcpConnectionPtr = std::shared_ptr<MockTcpConnection>;

TEST_CASE("MessageCodec - CommonMessage Serialization") {
    MessageCodec codec;
    
    SUBCASE("Serialize and deserialize CommonMessage") {
        // 创建测试消息
        CommonMessage original;
        original.type = MessageType::COMMAND;
        original.source_id = 123;
        original.target_id = 456;
        original.invoke_id = "test-invoke-id";
        original.data = {0x01, 0x02, 0x03, 0x04};
        original.b_lastmsg = true;
        
        // 序列化到Buffer
        Buffer buffer;
        bool encodeResult = codec.encode(original, &buffer);
        CHECK(encodeResult);
        CHECK(buffer.readableBytes() > sizeof(MessageFrameHeader));
        
        // 设置回调来接收反序列化的消息
        CommonMessage received;
        bool callbackCalled = false;
        
        codec.setCommonMessageCallback([&](const TcpConnectionPtr&, const CommonMessage& msg, Timestamp) {
            received = msg;
            callbackCalled = true;
        });
        
        // 模拟接收消息
        auto mockConn = std::make_shared<MockTcpConnection>();
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);
        
        // 验证回调被调用且数据正确
        CHECK(callbackCalled);
        CHECK(received.type == original.type);
        CHECK(received.source_id == original.source_id);
        CHECK(received.target_id == original.target_id);
        CHECK(received.invoke_id == original.invoke_id);
        CHECK(received.data == original.data);
        CHECK(received.b_lastmsg == original.b_lastmsg);
    }
    
    SUBCASE("Serialize CommonMessage with empty data") {
        CommonMessage original;
        original.type = MessageType::RESULT;
        original.source_id = 0;
        original.target_id = -1;
        original.invoke_id = "";
        original.data.clear();
        original.b_lastmsg = false;
        
        Buffer buffer;
        bool encodeResult = codec.encode(original, &buffer);
        CHECK(encodeResult);
        
        CommonMessage received;
        bool callbackCalled = false;
        
        codec.setCommonMessageCallback([&](const TcpConnectionPtr&, const CommonMessage& msg, Timestamp) {
            received = msg;
            callbackCalled = true;
        });
        
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);
        
        CHECK(callbackCalled);
        CHECK(received.type == original.type);
        CHECK(received.source_id == original.source_id);
        CHECK(received.target_id == original.target_id);
        CHECK(received.invoke_id == original.invoke_id);
        CHECK(received.data.empty());
        CHECK(received.b_lastmsg == original.b_lastmsg);
    }
}

TEST_CASE("MessageCodec - EventMessage Serialization") {
    MessageCodec codec;
    
    SUBCASE("Serialize and deserialize EventMessage") {
        // 创建测试消息
        EventMessage original;
        original.event_type = 100;
        original.device_uuid.device_id = 789;
        original.device_uuid.category = DeviceCategory::IED;
        original.source_id = 321;
        original.description = "Test event description";
        original.data = {0xAA, 0xBB, 0xCC, 0xDD, 0xEE};
        
        // 序列化到Buffer
        Buffer buffer;
        bool encodeResult = codec.encode(original, &buffer);
        CHECK(encodeResult);
        CHECK(buffer.readableBytes() > sizeof(MessageFrameHeader));
        
        // 设置回调来接收反序列化的消息
        EventMessage received;
        bool callbackCalled = false;
        
        codec.setEventMessageCallback([&](const TcpConnectionPtr&, const EventMessage& msg, Timestamp) {
            received = msg;
            callbackCalled = true;
        });
        
        // 模拟接收消息
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);
        
        // 验证回调被调用且数据正确
        CHECK(callbackCalled);
        CHECK(received.event_type == original.event_type);
        CHECK(received.device_uuid.device_id == original.device_uuid.device_id);
        CHECK(received.device_uuid.category == original.device_uuid.category);
        CHECK(received.source_id == original.source_id);
        CHECK(received.description == original.description);
        CHECK(received.data == original.data);
    }
    
    SUBCASE("Serialize EventMessage with different device categories") {
        EventMessage original;
        original.event_type = 200;
        original.device_uuid.device_id = 999;
        original.device_uuid.category = DeviceCategory::TRANSFORMER;
        original.source_id = 111;
        original.description = "Transformer event";
        original.data = {0x11, 0x22};
        
        Buffer buffer;
        bool encodeResult = codec.encode(original, &buffer);
        CHECK(encodeResult);
        
        EventMessage received;
        bool callbackCalled = false;
        
        codec.setEventMessageCallback([&](const TcpConnectionPtr&, const EventMessage& msg, Timestamp) {
            received = msg;
            callbackCalled = true;
        });
        
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);
        
        CHECK(callbackCalled);
        CHECK(received.device_uuid.category == DeviceCategory::TRANSFORMER);
    }
}

TEST_CASE("MessageCodec - Multiple Messages") {
    MessageCodec codec;
    
    SUBCASE("Process multiple messages in one buffer") {
        // 创建两个不同类型的消息
        CommonMessage commonMsg;
        commonMsg.type = MessageType::COMMAND;
        commonMsg.source_id = 1;
        commonMsg.target_id = 2;
        commonMsg.invoke_id = "msg1";
        commonMsg.data = {0x01};
        commonMsg.b_lastmsg = true;
        
        EventMessage eventMsg;
        eventMsg.event_type = 300;
        eventMsg.device_uuid.device_id = 3;
        eventMsg.device_uuid.category = DeviceCategory::SWITCH;
        eventMsg.source_id = 4;
        eventMsg.description = "msg2";
        eventMsg.data = {0x02};
        
        // 序列化两个消息到同一个buffer
        Buffer buffer;
        CHECK(codec.encode(commonMsg, &buffer));
        CHECK(codec.encode(eventMsg, &buffer));
        
        // 设置回调计数器
        int commonMsgCount = 0;
        int eventMsgCount = 0;
        
        codec.setCommonMessageCallback([&](const TcpConnectionPtr&, const CommonMessage&, Timestamp) {
            commonMsgCount++;
        });
        
        codec.setEventMessageCallback([&](const TcpConnectionPtr&, const EventMessage&, Timestamp) {
            eventMsgCount++;
        });
        
        // 处理消息
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);
        
        // 验证两个消息都被处理了
        CHECK(commonMsgCount == 1);
        CHECK(eventMsgCount == 1);
        CHECK(buffer.readableBytes() == 0);  // 所有数据都被消费了
    }
}

TEST_CASE("MessageCodec - Error Handling") {
    MessageCodec codec;
    
    SUBCASE("Handle incomplete message") {
        Buffer buffer;
        
        // 只写入部分帧头
        MessageFrameHeader header;
        header.magic = 0x12345678;
        header.type = MessageCodecType::COMMON_MESSAGE;
        header.length = 100;
        
        buffer.append(&header, sizeof(header) - 1);  // 缺少最后一个字节
        
        int callbackCount = 0;
        codec.setCommonMessageCallback([&](const TcpConnectionPtr&, const CommonMessage&, Timestamp) {
            callbackCount++;
        });
        
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);
        
        // 不应该触发回调，数据应该保留在buffer中
        CHECK(callbackCount == 0);
        CHECK(buffer.readableBytes() == sizeof(header) - 1);
    }
    
    SUBCASE("Handle invalid magic number") {
        Buffer buffer;
        
        MessageFrameHeader header;
        header.magic = 0xDEADBEEF;  // 错误的魔数
        header.type = MessageCodecType::COMMON_MESSAGE;
        header.length = 0;
        
        buffer.append(&header, sizeof(header));
        
        int callbackCount = 0;
        codec.setCommonMessageCallback([&](const TcpConnectionPtr&, const CommonMessage&, Timestamp) {
            callbackCount++;
        });
        
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);
        
        // 不应该触发回调，buffer应该被清空
        CHECK(callbackCount == 0);
        CHECK(buffer.readableBytes() == 0);
    }

    SUBCASE("Handle message too large") {
        Buffer buffer;

        MessageFrameHeader header;
        header.magic = 0x12345678;
        header.type = MessageCodecType::COMMON_MESSAGE;
        header.length = 100 * 1024 * 1024;  // 100MB，超过限制

        buffer.append(&header, sizeof(header));

        int callbackCount = 0;
        codec.setCommonMessageCallback([&](const TcpConnectionPtr&, const CommonMessage&, Timestamp) {
            callbackCount++;
        });

        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);

        // 不应该触发回调，buffer应该被清空
        CHECK(callbackCount == 0);
        CHECK(buffer.readableBytes() == 0);
    }
}

TEST_CASE("MessageCodec - Callback Management") {
    MessageCodec codec;

    SUBCASE("No callback set") {
        CommonMessage msg;
        msg.type = MessageType::COMMAND;
        msg.source_id = 1;
        msg.target_id = 2;
        msg.invoke_id = "test";
        msg.data = {0x01};
        msg.b_lastmsg = true;

        Buffer buffer;
        CHECK(codec.encode(msg, &buffer));

        // 没有设置回调，应该不会崩溃
        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);

        // buffer应该被消费完
        CHECK(buffer.readableBytes() == 0);
    }

    SUBCASE("Mixed message types with partial callbacks") {
        CommonMessage commonMsg;
        commonMsg.type = MessageType::COMMAND;
        commonMsg.source_id = 1;
        commonMsg.target_id = 2;
        commonMsg.invoke_id = "common";
        commonMsg.data = {0x01};
        commonMsg.b_lastmsg = true;

        EventMessage eventMsg;
        eventMsg.event_type = 100;
        eventMsg.device_uuid.device_id = 3;
        eventMsg.device_uuid.category = DeviceCategory::IED;
        eventMsg.source_id = 4;
        eventMsg.description = "event";
        eventMsg.data = {0x02};

        Buffer buffer;
        CHECK(codec.encode(commonMsg, &buffer));
        CHECK(codec.encode(eventMsg, &buffer));

        // 只设置CommonMessage回调
        int commonCallbackCount = 0;
        codec.setCommonMessageCallback([&](const TcpConnectionPtr&, const CommonMessage&, Timestamp) {
            commonCallbackCount++;
        });

        auto receiveTime = std::chrono::system_clock::now();
        codec.onMessage(nullptr, &buffer, receiveTime);

        // 只有CommonMessage回调应该被调用
        CHECK(commonCallbackCount == 1);
        CHECK(buffer.readableBytes() == 0);  // 所有消息都被处理了
    }
}

TEST_CASE("MessageCodec - Send Methods") {
    MessageCodec codec;

    SUBCASE("Send CommonMessage") {
        auto mockConn = std::make_shared<MockTcpConnection>();

        CommonMessage msg;
        msg.type = MessageType::RESULT;
        msg.source_id = 100;
        msg.target_id = 200;
        msg.invoke_id = "send-test";
        msg.data = {0xAA, 0xBB};
        msg.b_lastmsg = false;

        // 这个测试需要修改，因为send方法需要真正的TcpConnectionPtr
        // 暂时跳过实际的send测试，只测试encode
        Buffer buffer;
        bool result = codec.encode(msg, &buffer);
        CHECK(result);
        CHECK(buffer.readableBytes() > 0);
    }

    SUBCASE("Send EventMessage") {
        EventMessage msg;
        msg.event_type = 500;
        msg.device_uuid.device_id = 999;
        msg.device_uuid.category = DeviceCategory::UNKNOWN;
        msg.source_id = 888;
        msg.description = "send event test";
        msg.data = {0x11, 0x22, 0x33};

        Buffer buffer;
        bool result = codec.encode(msg, &buffer);
        CHECK(result);
        CHECK(buffer.readableBytes() > 0);
    }
}

TEST_CASE("MessageCodec - Frame Header Validation") {
    SUBCASE("Frame header size") {
        // 确保帧头大小是预期的
        CHECK(sizeof(MessageFrameHeader) >= 12);  // 至少包含magic(4) + type(1) + length(4) + version(1) + reserved(2)
    }

    SUBCASE("Message type values") {
        CHECK(static_cast<uint8_t>(MessageCodecType::COMMON_MESSAGE) == 0x01);
        CHECK(static_cast<uint8_t>(MessageCodecType::EVENT_MESSAGE) == 0x02);
    }
}
