/**
 * @file message_codec.hpp
 * @brief 消息编解码器 - 支持CommonMessage和EventMessage的序列化/反序列化
 * <AUTHOR> project
 * @date 2025-08-22
 */

#ifndef ZEXUAN_NET_MESSAGE_CODEC_HPP
#define ZEXUAN_NET_MESSAGE_CODEC_HPP

#include <functional>
#include <memory>
#include <vector>
#include <cstdint>

#include "zexuan/base/types/structs.hpp"
#include "zexuan/net/buffer.hpp"
#include "zexuan/net/tcp_connection.hpp"

namespace zexuan {
  namespace net {

    // 前向声明
    class Buffer;
    class TcpConnection;
    using TcpConnectionPtr = std::shared_ptr<TcpConnection>;

    /// @brief 消息类型枚举
    enum class MessageCodecType : uint8_t {
      COMMON_MESSAGE = 0x01,    ///< CommonMessage类型
      EVENT_MESSAGE = 0x02      ///< EventMessage类型
    };

    /// @brief 消息帧头结构
    struct MessageFrameHeader {
      uint32_t magic{0x12345678};           ///< 魔数，用于帧同步
      MessageCodecType type;                ///< 消息类型
      uint32_t length{0};                   ///< 消息体长度（不包含头部）
      uint8_t version{1};                   ///< 协议版本
      uint8_t reserved[2]{0};               ///< 保留字段
    } __attribute__((packed));

    /// @brief 消息回调函数类型
    using CommonMessageCallback = std::function<void(const TcpConnectionPtr&, 
                                                    const zexuan::base::CommonMessage&, 
                                                    Timestamp)>;
    using EventMessageCallback = std::function<void(const TcpConnectionPtr&, 
                                                   const zexuan::base::EventMessage&, 
                                                   Timestamp)>;

    /// @brief 消息编解码器类
    /// 
    /// 负责CommonMessage和EventMessage的序列化/反序列化
    /// 采用回调方式处理解码后的消息，保持松耦合设计
    class MessageCodec {
    public:
      /// @brief 构造函数
      MessageCodec();
      
      /// @brief 析构函数
      ~MessageCodec() = default;

      // 禁用拷贝构造和赋值
      MessageCodec(const MessageCodec&) = delete;
      MessageCodec& operator=(const MessageCodec&) = delete;

      /// @brief 设置CommonMessage回调
      /// @param cb 回调函数
      void setCommonMessageCallback(const CommonMessageCallback& cb) {
        commonMessageCallback_ = cb;
      }

      /// @brief 设置EventMessage回调
      /// @param cb 回调函数
      void setEventMessageCallback(const EventMessageCallback& cb) {
        eventMessageCallback_ = cb;
      }

      /// @brief 处理接收到的数据（在onMessage中调用）
      /// @param conn TCP连接
      /// @param buffer 接收缓冲区
      /// @param receiveTime 接收时间
      void onMessage(const TcpConnectionPtr& conn, Buffer* buffer, Timestamp receiveTime);

      /// @brief 编码CommonMessage
      /// @param message 要编码的消息
      /// @param buffer 输出缓冲区
      /// @return 编码是否成功
      bool encode(const zexuan::base::CommonMessage& message, Buffer* buffer);

      /// @brief 编码EventMessage
      /// @param message 要编码的消息
      /// @param buffer 输出缓冲区
      /// @return 编码是否成功
      bool encode(const zexuan::base::EventMessage& message, Buffer* buffer);

      /// @brief 发送CommonMessage
      /// @param conn TCP连接
      /// @param message 要发送的消息
      void send(const TcpConnectionPtr& conn, const zexuan::base::CommonMessage& message);

      /// @brief 发送EventMessage
      /// @param conn TCP连接
      /// @param message 要发送的消息
      void send(const TcpConnectionPtr& conn, const zexuan::base::EventMessage& message);

    private:
      /// @brief 尝试解析一个完整的消息帧
      /// @param conn TCP连接
      /// @param buffer 接收缓冲区
      /// @param receiveTime 接收时间
      /// @return 是否成功解析了一个完整帧
      bool tryParseMessage(const TcpConnectionPtr& conn, Buffer* buffer, Timestamp receiveTime);

      /// @brief 序列化CommonMessage到字节流
      /// @param message 消息对象
      /// @param data 输出字节流
      /// @return 序列化是否成功
      bool serializeCommonMessage(const zexuan::base::CommonMessage& message, 
                                 std::vector<uint8_t>& data);

      /// @brief 反序列化CommonMessage从字节流
      /// @param data 输入字节流
      /// @param message 输出消息对象
      /// @return 反序列化是否成功
      bool deserializeCommonMessage(const std::vector<uint8_t>& data, 
                                   zexuan::base::CommonMessage& message);

      /// @brief 序列化EventMessage到字节流
      /// @param message 消息对象
      /// @param data 输出字节流
      /// @return 序列化是否成功
      bool serializeEventMessage(const zexuan::base::EventMessage& message, 
                                std::vector<uint8_t>& data);

      /// @brief 反序列化EventMessage从字节流
      /// @param data 输入字节流
      /// @param message 输出消息对象
      /// @return 反序列化是否成功
      bool deserializeEventMessage(const std::vector<uint8_t>& data, 
                                  zexuan::base::EventMessage& message);

      /// @brief 写入基本类型到字节流
      template<typename T>
      void writeBasicType(std::vector<uint8_t>& data, const T& value);

      /// @brief 从字节流读取基本类型
      template<typename T>
      bool readBasicType(const std::vector<uint8_t>& data, size_t& offset, T& value);

      /// @brief 写入字符串到字节流
      void writeString(std::vector<uint8_t>& data, const std::string& str);

      /// @brief 从字节流读取字符串
      bool readString(const std::vector<uint8_t>& data, size_t& offset, std::string& str);

      /// @brief 写入字节数组到字节流
      void writeByteArray(std::vector<uint8_t>& data, const std::vector<uint8_t>& bytes);

      /// @brief 从字节流读取字节数组
      bool readByteArray(const std::vector<uint8_t>& data, size_t& offset, std::vector<uint8_t>& bytes);

    private:
      CommonMessageCallback commonMessageCallback_;   ///< CommonMessage回调
      EventMessageCallback eventMessageCallback_;     ///< EventMessage回调
      
      static constexpr size_t kHeaderSize = sizeof(MessageFrameHeader);  ///< 帧头大小
      static constexpr uint32_t kMagicNumber = 0x12345678;               ///< 魔数
      static constexpr size_t kMaxMessageSize = 64 * 1024 * 1024;        ///< 最大消息大小(64MB)
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_MESSAGE_CODEC_HPP
