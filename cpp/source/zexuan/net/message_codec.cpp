#include "zexuan/net/message_codec.hpp"

#include <spdlog/spdlog.h>
#include <cstring>
#include <arpa/inet.h>  // for htonl, ntohl

using namespace zexuan::net;
using namespace zexuan::base;

MessageCodec::MessageCodec() {
  spdlog::debug("MessageCodec created");
}

void MessageCodec::onMessage(const TcpConnectionPtr& conn, Buffer* buffer, Timestamp receiveTime) {
  // 持续尝试解析消息，直到缓冲区中没有完整的消息帧
  while (tryParseMessage(conn, buffer, receiveTime)) {
    // 继续解析下一个消息
  }
}

bool MessageCodec::tryParseMessage(const TcpConnectionPtr& conn, Buffer* buffer, Timestamp receiveTime) {
  // 检查是否有足够的数据读取帧头
  if (buffer->readableBytes() < kHeaderSize) {
    return false;  // 数据不足，等待更多数据
  }

  // 读取帧头（不移动读指针）
  MessageFrameHeader header;
  std::memcpy(&header, buffer->peek(), kHeaderSize);
  
  // 转换字节序
  header.magic = ntohl(header.magic);
  header.length = ntohl(header.length);

  // 验证魔数
  if (header.magic != kMagicNumber) {
    spdlog::error("Invalid magic number: 0x{:08x}, expected: 0x{:08x}", 
                  header.magic, kMagicNumber);
    buffer->retrieveAll();  // 丢弃所有数据
    return false;
  }

  // 检查消息长度是否合理
  if (header.length > kMaxMessageSize) {
    spdlog::error("Message too large: {} bytes, max: {} bytes", 
                  header.length, kMaxMessageSize);
    buffer->retrieveAll();  // 丢弃所有数据
    return false;
  }

  // 检查是否有完整的消息（头部 + 消息体）
  size_t totalSize = kHeaderSize + header.length;
  if (buffer->readableBytes() < totalSize) {
    return false;  // 消息不完整，等待更多数据
  }

  // 跳过帧头
  buffer->retrieve(kHeaderSize);

  // 读取消息体
  std::vector<uint8_t> messageData(header.length);
  std::memcpy(messageData.data(), buffer->peek(), header.length);
  buffer->retrieve(header.length);

  // 根据消息类型进行反序列化和回调
  switch (header.type) {
    case MessageCodecType::COMMON_MESSAGE: {
      if (commonMessageCallback_) {
        CommonMessage message;
        if (deserializeCommonMessage(messageData, message)) {
          commonMessageCallback_(conn, message, receiveTime);
        } else {
          spdlog::error("Failed to deserialize CommonMessage");
        }
      } else {
        spdlog::warn("CommonMessage callback not set");
      }
      break;
    }
    
    case MessageCodecType::EVENT_MESSAGE: {
      if (eventMessageCallback_) {
        EventMessage message;
        if (deserializeEventMessage(messageData, message)) {
          eventMessageCallback_(conn, message, receiveTime);
        } else {
          spdlog::error("Failed to deserialize EventMessage");
        }
      } else {
        spdlog::warn("EventMessage callback not set");
      }
      break;
    }
    
    default:
      spdlog::error("Unknown message type: {}", static_cast<uint8_t>(header.type));
      break;
  }

  return true;  // 成功解析了一个消息
}

bool MessageCodec::encode(const CommonMessage& message, Buffer* buffer) {
  std::vector<uint8_t> messageData;
  if (!serializeCommonMessage(message, messageData)) {
    return false;
  }

  // 构造帧头
  MessageFrameHeader header;
  header.magic = htonl(kMagicNumber);
  header.type = MessageCodecType::COMMON_MESSAGE;
  header.length = htonl(static_cast<uint32_t>(messageData.size()));
  header.version = 1;

  // 写入帧头
  buffer->append(&header, kHeaderSize);
  
  // 写入消息体
  buffer->append(messageData.data(), messageData.size());

  return true;
}

bool MessageCodec::encode(const EventMessage& message, Buffer* buffer) {
  std::vector<uint8_t> messageData;
  if (!serializeEventMessage(message, messageData)) {
    return false;
  }

  // 构造帧头
  MessageFrameHeader header;
  header.magic = htonl(kMagicNumber);
  header.type = MessageCodecType::EVENT_MESSAGE;
  header.length = htonl(static_cast<uint32_t>(messageData.size()));
  header.version = 1;

  // 写入帧头
  buffer->append(&header, kHeaderSize);
  
  // 写入消息体
  buffer->append(messageData.data(), messageData.size());

  return true;
}

void MessageCodec::send(const TcpConnectionPtr& conn, const CommonMessage& message) {
  Buffer buffer;
  if (encode(message, &buffer)) {
    conn->send(&buffer);
  } else {
    spdlog::error("Failed to encode CommonMessage");
  }
}

void MessageCodec::send(const TcpConnectionPtr& conn, const EventMessage& message) {
  Buffer buffer;
  if (encode(message, &buffer)) {
    conn->send(&buffer);
  } else {
    spdlog::error("Failed to encode EventMessage");
  }
}

bool MessageCodec::serializeCommonMessage(const CommonMessage& message, std::vector<uint8_t>& data) {
  try {
    data.clear();
    
    // 序列化各个字段
    writeBasicType(data, static_cast<int>(message.type));
    writeBasicType(data, message.source_id);
    writeBasicType(data, message.target_id);
    writeString(data, message.invoke_id);
    writeByteArray(data, message.data);
    writeBasicType(data, message.b_lastmsg);
    
    return true;
  } catch (const std::exception& e) {
    spdlog::error("Failed to serialize CommonMessage: {}", e.what());
    return false;
  }
}

bool MessageCodec::deserializeCommonMessage(const std::vector<uint8_t>& data, CommonMessage& message) {
  try {
    size_t offset = 0;
    
    // 反序列化各个字段
    int type_int;
    if (!readBasicType(data, offset, type_int)) return false;
    message.type = static_cast<MessageType>(type_int);
    
    if (!readBasicType(data, offset, message.source_id)) return false;
    if (!readBasicType(data, offset, message.target_id)) return false;
    if (!readString(data, offset, message.invoke_id)) return false;
    if (!readByteArray(data, offset, message.data)) return false;
    if (!readBasicType(data, offset, message.b_lastmsg)) return false;
    
    return true;
  } catch (const std::exception& e) {
    spdlog::error("Failed to deserialize CommonMessage: {}", e.what());
    return false;
  }
}

bool MessageCodec::serializeEventMessage(const EventMessage& message, std::vector<uint8_t>& data) {
  try {
    data.clear();
    
    // 序列化各个字段
    writeBasicType(data, message.event_type);
    writeBasicType(data, message.device_uuid.device_id);
    writeBasicType(data, static_cast<int>(message.device_uuid.category));
    writeBasicType(data, message.source_id);
    writeString(data, message.description);
    writeByteArray(data, message.data);
    
    return true;
  } catch (const std::exception& e) {
    spdlog::error("Failed to serialize EventMessage: {}", e.what());
    return false;
  }
}

bool MessageCodec::deserializeEventMessage(const std::vector<uint8_t>& data, EventMessage& message) {
  try {
    size_t offset = 0;
    
    // 反序列化各个字段
    if (!readBasicType(data, offset, message.event_type)) return false;
    if (!readBasicType(data, offset, message.device_uuid.device_id)) return false;
    
    int category_int;
    if (!readBasicType(data, offset, category_int)) return false;
    message.device_uuid.category = static_cast<DeviceCategory>(category_int);
    
    if (!readBasicType(data, offset, message.source_id)) return false;
    if (!readString(data, offset, message.description)) return false;
    if (!readByteArray(data, offset, message.data)) return false;
    
    return true;
  } catch (const std::exception& e) {
    spdlog::error("Failed to deserialize EventMessage: {}", e.what());
    return false;
  }
}

// 模板函数实现
template<typename T>
void MessageCodec::writeBasicType(std::vector<uint8_t>& data, const T& value) {
  const uint8_t* ptr = reinterpret_cast<const uint8_t*>(&value);
  data.insert(data.end(), ptr, ptr + sizeof(T));
}

template<typename T>
bool MessageCodec::readBasicType(const std::vector<uint8_t>& data, size_t& offset, T& value) {
  if (offset + sizeof(T) > data.size()) {
    return false;
  }

  std::memcpy(&value, data.data() + offset, sizeof(T));
  offset += sizeof(T);
  return true;
}

void MessageCodec::writeString(std::vector<uint8_t>& data, const std::string& str) {
  // 写入字符串长度
  uint32_t length = htonl(static_cast<uint32_t>(str.size()));
  writeBasicType(data, length);

  // 写入字符串内容
  data.insert(data.end(), str.begin(), str.end());
}

bool MessageCodec::readString(const std::vector<uint8_t>& data, size_t& offset, std::string& str) {
  // 读取字符串长度
  uint32_t length;
  if (!readBasicType(data, offset, length)) {
    return false;
  }
  length = ntohl(length);

  // 检查数据是否足够
  if (offset + length > data.size()) {
    return false;
  }

  // 读取字符串内容
  str.assign(reinterpret_cast<const char*>(data.data() + offset), length);
  offset += length;
  return true;
}

void MessageCodec::writeByteArray(std::vector<uint8_t>& data, const std::vector<uint8_t>& bytes) {
  // 写入数组长度
  uint32_t length = htonl(static_cast<uint32_t>(bytes.size()));
  writeBasicType(data, length);

  // 写入数组内容
  data.insert(data.end(), bytes.begin(), bytes.end());
}

bool MessageCodec::readByteArray(const std::vector<uint8_t>& data, size_t& offset, std::vector<uint8_t>& bytes) {
  // 读取数组长度
  uint32_t length;
  if (!readBasicType(data, offset, length)) {
    return false;
  }
  length = ntohl(length);

  // 检查数据是否足够
  if (offset + length > data.size()) {
    return false;
  }

  // 读取数组内容
  bytes.assign(data.begin() + offset, data.begin() + offset + length);
  offset += length;
  return true;
}

// 显式实例化模板函数
template void MessageCodec::writeBasicType<int>(std::vector<uint8_t>&, const int&);
template void MessageCodec::writeBasicType<bool>(std::vector<uint8_t>&, const bool&);
template void MessageCodec::writeBasicType<uint32_t>(std::vector<uint8_t>&, const uint32_t&);

template bool MessageCodec::readBasicType<int>(const std::vector<uint8_t>&, size_t&, int&);
template bool MessageCodec::readBasicType<bool>(const std::vector<uint8_t>&, size_t&, bool&);
template bool MessageCodec::readBasicType<uint32_t>(const std::vector<uint8_t>&, size_t&, uint32_t&);
